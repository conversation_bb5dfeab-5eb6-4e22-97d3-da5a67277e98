import { act, renderHook } from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  ActionConfig,
  ExtendedActionConfig,
} from '../models/action.config';
import { useClientActionAsync } from './useClientActionsAsync';

// Mock all dependencies to keep tests simple
vi.mock('../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
}));

vi.mock('../helpers/render-template', () => ({
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

// vi.mock('../useAppStore', () => ({
//   useAppStore: {
//     getState: vi.fn(() => ({ mockState: 'test' })),
//   },
// }));

vi.mock('./useModalStore', () => ({
  useModalStore: vi.fn(() => vi.fn()),
}));

vi.mock('./useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn(() => vi.fn()),
}));

vi.mock('./useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('./useNotesStore', () => ({
  useNotesStore: {
    getState: vi.fn(() => ({
      addNote: vi.fn(),
    })),
  },
}));

vi.mock('./useReminderStore', () => ({
  useReminderStore: {
    getState: vi.fn(() => ({
      addReminder: vi.fn(),
    })),
  },
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    getValues: vi.fn(() => ({ testField: 'testValue' })),
    resetField: vi.fn(),
    watch: vi.fn(() => ({ testField: 'testValue' })),
    formState: {},
  })),
}));

describe('useClientAction', () => {
  let mockNavigate: ReturnType<typeof vi.fn>;
  let mockLocation: Location;
  let mockKeycloak: Keycloak;

  beforeEach(() => {
    mockNavigate = vi.fn();
    mockLocation = {
      search: '',
      pathname: '/test',
      hash: '',
      state: null,
      key: 'test-key',
    };
    mockKeycloak = {
      token: 'mock-token',
      init: vi.fn(),
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      accountManagement: vi.fn(),
      createLoginUrl: vi.fn(),
      createLogoutUrl: vi.fn(),
      createRegisterUrl: vi.fn(),
      createAccountUrl: vi.fn(),
      isTokenExpired: vi.fn(() => false),
      updateToken: vi.fn(),
      clearToken: vi.fn(),
      hasRealmRole: vi.fn(),
      hasResourceRole: vi.fn(),
      loadUserProfile: vi.fn(),
      loadUserInfo: vi.fn(),
      authenticated: true,
      tokenParsed: {},
      refreshToken: 'mock-refresh-token',
      refreshTokenParsed: {},
      idToken: 'mock-id-token',
      idTokenParsed: {},
      realmAccess: { roles: [] },
      resourceAccess: {},
      timeSkew: 0,
      responseMode: 'fragment',
      flow: 'standard',
      responseType: 'code',
    } as Keycloak;
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize the hook successfully', () => {
    const { result } = renderHook(() =>
      useClientActionAsync({
        navigate: mockNavigate,
        location: mockLocation,
        keycloak: mockKeycloak,
      })
    );

    expect(result.current).toBeDefined();
    expect(result.current.callClientAction).toBeInstanceOf(Function);
    expect(result.current.callClientActionAsync).toBeInstanceOf(Function);
    expect(result.current.callClientActionsSequentially).toBeInstanceOf(
      Function
    );
    expect(result.current.callClientActionsConcurrently).toBeInstanceOf(
      Function
    );
    expect(result.current.callClientActionsWithLimit).toBeInstanceOf(Function);
    expect(result.current.executeClientActionAsynchronously).toBeInstanceOf(
      Function
    );
    expect(result.current.executeClientActionSynchronously).toBeInstanceOf(
      Function
    );
    expect(result.current.withAsync).toBeInstanceOf(Function);
    expect(result.current.createBatch).toBeInstanceOf(Function);
  });

  describe('Basic Actions', () => {
    it('should handle log action', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const logAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
      };

      result.current.executeClientActionSynchronously(logAction);
      expect(consoleSpy).toHaveBeenCalledWith('test message');
      consoleSpy.mockRestore();
    });

    it('should handle navigate action', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const navigateAction: ActionConfig = {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/new-path'],
      };

      result.current.executeClientActionSynchronously(navigateAction);
      expect(mockNavigate).toHaveBeenCalledWith(
        { pathname: '/new-path', search: '' },
        undefined
      );
    });

    it('should handle logout action', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const logoutAction: ActionConfig = {
        type: 'clientAction',
        action: 'logout',
        payload: [],
      };

      result.current.executeClientActionSynchronously(logoutAction);
      expect(mockKeycloak.logout).toHaveBeenCalled();
    });
  });

  describe('Async Actions', () => {
    it('should handle async log action', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const asyncLogAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['async test message'],
        async: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(asyncLogAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('async test message');
      consoleSpy.mockRestore();
    });
  });

  describe('Array Actions', () => {
    it('should handle empty array', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const returnValue = result.current.callClientAction([]);
      expect(returnValue).toBeUndefined();
    });

    it('should handle sequential actions', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['first'] },
        { type: 'clientAction', action: 'log', payload: ['second'] },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, 'first');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'second');
      consoleSpy.mockRestore();
    });

    it('should handle concurrent actions', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['concurrent1'] },
        { type: 'clientAction', action: 'log', payload: ['concurrent2'] },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });

    it('should handle actions with concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['limit1'] },
        { type: 'clientAction', action: 'log', payload: ['limit2'] },
        { type: 'clientAction', action: 'log', payload: ['limit3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(3);
      consoleSpy.mockRestore();
    });
  });

  describe('callClientAction Factory Function', () => {
    it('should handle single action synchronously by default', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['sync test'],
      };

      const returnValue = result.current.callClientAction(action);
      expect(returnValue).toBeUndefined();
      expect(consoleSpy).toHaveBeenCalledWith('sync test');
      consoleSpy.mockRestore();
    });

    it('should handle single action asynchronously when async=true', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['async param test'],
      };

      await act(async () => {
        const promise = result.current.callClientAction(action, true);
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(consoleSpy).toHaveBeenCalledWith('async param test');
      consoleSpy.mockRestore();
    });

    it('should handle array with concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['batch1'] },
        { type: 'clientAction', action: 'log', payload: ['batch2'] },
      ];

      await act(async () => {
        await result.current.callClientAction(actions, false, 1);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });

    it('should handle array sequentially when async=true', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['seq1'] },
        { type: 'clientAction', action: 'log', payload: ['seq2'] },
      ];

      await act(async () => {
        await result.current.callClientAction(actions, true);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });

    it('should handle array concurrently by default', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['default1'] },
        { type: 'clientAction', action: 'log', payload: ['default2'] },
      ];

      await act(async () => {
        await result.current.callClientAction(actions);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });
  });

  describe('Modal Actions', () => {
    it('should handle triggerModal action', () => {
      const mockSetModalState = vi.fn();
      vi.mocked(vi.fn()).mockReturnValue(mockSetModalState);

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const modalAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerModal',
        payload: [{ title: 'Test Modal', content: 'Test content' }],
      };

      result.current.executeClientActionSynchronously(modalAction);
      // Modal state setting is mocked, so we just verify the action doesn't throw
      expect(result.current).toBeDefined();
    });

    it('should handle closeModal action', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const closeModalAction: ActionConfig = {
        type: 'clientAction',
        action: 'closeModal',
        payload: [],
      };

      result.current.executeClientActionSynchronously(closeModalAction);
      // Modal state setting is mocked, so we just verify the action doesn't throw
      expect(result.current).toBeDefined();
    });
  });

  describe('Store Actions', () => {
    it('should handle clearStore action with empty payload', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const clearStoreAction: ActionConfig = {
        type: 'clientAction',
        action: 'clearStore',
        payload: [],
      };

      result.current.executeClientActionSynchronously(clearStoreAction);
      expect(result.current).toBeDefined();
    });

    it('should handle clearStore action with specific keys', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const clearStoreAction: ActionConfig = {
        type: 'clientAction',
        action: 'clearStore',
        payload: ['key1', 'key2'],
      };

      result.current.executeClientActionSynchronously(clearStoreAction);
      expect(result.current).toBeDefined();
    });

    it('should handle updateStore action', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const updateStoreAction: ActionConfig = {
        type: 'clientAction',
        action: 'updateStore',
        payload: [{ newKey: 'newValue', anotherKey: 'anotherValue' }],
      };

      result.current.executeClientActionSynchronously(updateStoreAction);
      expect(result.current).toBeDefined();
    });

    it('should handle updateStore action with empty payload', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const updateStoreAction: ActionConfig = {
        type: 'clientAction',
        action: 'updateStore',
        payload: [],
      };

      result.current.executeClientActionSynchronously(updateStoreAction);
      expect(result.current).toBeDefined();
    });
  });

  describe('Error Handling Actions', () => {
    it('should handle clearErrors action without payload', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const clearErrorsAction: ActionConfig = {
        type: 'clientAction',
        action: 'clearErrors',
        payload: undefined,
      };

      result.current.executeClientActionSynchronously(clearErrorsAction);
      expect(result.current).toBeDefined();
    });

    it('should handle clearErrors action with specific error key', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const clearErrorsAction: ActionConfig = {
        type: 'clientAction',
        action: 'clearErrors',
        payload: 'specific-error-key',
      };

      result.current.executeClientActionSynchronously(clearErrorsAction);
      expect(result.current).toBeDefined();
    });
  });

  describe('Timeout Actions', () => {
    it('should handle timeout action', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const timeoutAction: ActionConfig = {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          100,
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['timeout executed'],
            },
          ],
        ],
      };

      result.current.executeClientActionSynchronously(timeoutAction);
      expect(result.current).toBeDefined();
      consoleSpy.mockRestore();
    });
  });

  describe('Conditional Actions', () => {
    it('should handle conditional action with true condition', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const conditionalAction: ActionConfig = {
        type: 'clientAction',
        action: 'conditional',
        payload: {
          condition: true,
          actions: {
            whenTrue: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['condition was true'],
              },
            ],
            whenFalse: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['condition was false'],
              },
            ],
          },
        },
      };

      await act(async () => {
        await result.current.executeClientActionAsynchronously(
          conditionalAction
        );
      });

      expect(consoleSpy).toHaveBeenCalledWith('condition was true');
      consoleSpy.mockRestore();
    });

    it('should handle conditional action with false condition', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const conditionalAction: ActionConfig = {
        type: 'clientAction',
        action: 'conditional',
        payload: {
          condition: false,
          actions: {
            whenTrue: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['condition was true'],
              },
            ],
            whenFalse: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['condition was false'],
              },
            ],
          },
        },
      };

      await act(async () => {
        await result.current.executeClientActionAsynchronously(
          conditionalAction
        );
      });

      expect(consoleSpy).toHaveBeenCalledWith('condition was false');
      consoleSpy.mockRestore();
    });
  });

  describe('Form Actions', () => {
    it('should handle resetFields action with string fields', () => {
      const mockResetField = vi.fn();

      // Mock the useFormContext hook to return our mock
      vi.mocked(vi.fn()).mockReturnValue({
        resetField: mockResetField,
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const resetFieldsAction: ActionConfig = {
        type: 'clientAction',
        action: 'resetFields',
        payload: {
          fields: ['field1', 'field2'],
        },
      };

      result.current.executeClientActionSynchronously(resetFieldsAction);
      // Since we're using mocked form context, we just verify the action doesn't throw
      expect(result.current).toBeDefined();
    });

    it('should handle resetFields action with object fields', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const resetFieldsAction: ActionConfig = {
        type: 'clientAction',
        action: 'resetFields',
        payload: {
          fields: [
            { fieldName: 'field1', defaultValue: 'newDefault' },
            { fieldName: 'field2', defaultValue: 'anotherDefault' },
          ],
        },
      };

      result.current.executeClientActionSynchronously(resetFieldsAction);
      expect(result.current).toBeDefined();
    });
  });

  describe('Fetch Actions', () => {
    it('should handle triggerFetchCall action', async () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const fetchAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [
          {
            key: 'testFetch',
            url: '/api/test',
            method: 'GET',
          },
        ],
      };

      await act(async () => {
        await result.current.executeClientActionAsynchronously(fetchAction);
      });

      expect(result.current).toBeDefined();
    });

    it('should handle triggerFetchCall action without keycloak', async () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          // No keycloak provided
        })
      );

      const fetchAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [
          {
            key: 'testFetch',
            url: '/api/test',
            method: 'GET',
          },
        ],
      };

      await act(async () => {
        await result.current.executeClientActionAsynchronously(fetchAction);
      });

      expect(result.current).toBeDefined();
    });
  });

  describe('Default Modal Action', () => {
    it('should handle defaultModalAction with function payload', () => {
      const mockFunction = vi.fn();
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const defaultModalAction: ActionConfig = {
        type: 'clientAction',
        action: 'defaultModalAction',
        payload: mockFunction,
      };

      result.current.executeClientActionSynchronously(defaultModalAction);
      expect(mockFunction).toHaveBeenCalled();
    });

    it('should handle defaultModalAction with non-function payload', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const defaultModalAction: ActionConfig = {
        type: 'clientAction',
        action: 'defaultModalAction',
        payload: 'not a function',
      };

      result.current.executeClientActionSynchronously(defaultModalAction);
      expect(result.current).toBeDefined();
    });
  });

  describe('Unknown Actions', () => {
    it('should handle unknown action gracefully', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          debug: true, // Enable debug mode to activate logger
        })
      );

      const unknownAction = {
        type: 'clientAction',
        action: 'unknownAction',
        payload: [],
      } as unknown as ActionConfig;

      // Should not throw an error
      expect(() =>
        result.current.executeClientActionSynchronously(unknownAction)
      ).not.toThrow();
    });
  });

  describe('Navigation with Parameters', () => {
    it('should handle navigate action with clearParams option', () => {
      mockLocation.search = '?existing=param';

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const navigateAction: ActionConfig = {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/new-path', { clearParams: true }],
      };

      result.current.executeClientActionSynchronously(navigateAction);
      expect(mockNavigate).toHaveBeenCalledWith(
        { pathname: '/new-path', search: '' },
        {}
      );
    });

    it('should handle navigate action preserving existing params', () => {
      mockLocation.search = '?existing=param';

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const navigateAction: ActionConfig = {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/new-path', { replace: true }],
      };

      result.current.executeClientActionSynchronously(navigateAction);
      expect(mockNavigate).toHaveBeenCalledWith(
        { pathname: '/new-path', search: 'existing=param' },
        { replace: true }
      );
    });
  });

  describe('Extended Action Config', () => {
    it('should handle action with async property set to true', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const extendedAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['extended async test'],
        async: true,
      };

      await act(async () => {
        const promise = result.current.callClientAction(extendedAction);
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(consoleSpy).toHaveBeenCalledWith('extended async test');
      consoleSpy.mockRestore();
    });

    it('should handle action with async property set to false', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const extendedAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['extended sync test'],
        async: false,
      };

      const returnValue = result.current.callClientAction(extendedAction);
      expect(returnValue).toBeUndefined();
      expect(consoleSpy).toHaveBeenCalledWith('extended sync test');
      consoleSpy.mockRestore();
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle null/undefined action gracefully', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          debug: true, // Enable debug mode to activate logger
        })
      );

      // Test with null - should now handle gracefully and not throw
      expect(() =>
        result.current.callClientAction(null as unknown as ActionConfig)
      ).not.toThrow();

      // Test with undefined - should now handle gracefully and not throw
      expect(() =>
        result.current.callClientAction(undefined as unknown as ActionConfig)
      ).not.toThrow();
    });

    it('should handle empty array input gracefully', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Empty array should return undefined and not throw
      const returnValue = result.current.callClientAction([]);
      expect(returnValue).toBeUndefined();
    });
    it('should handle empty payload gracefully', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      const actionWithEmptyPayload: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: [],
      };
      expect(() =>
        result.current.executeClientActionSynchronously(actionWithEmptyPayload)
      ).not.toThrow();
    });
    it('should handle malformed action config gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {
        // Mock implementation
      });
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Test 1: Missing 'type' property
      const missingTypeAction = {
        action: 'log',
        payload: ['test'],
      } as unknown as ActionConfig;

      expect(() =>
        result.current.executeClientActionSynchronously(missingTypeAction)
      ).not.toThrow();

      // Test 2: Missing 'action' property
      const missingActionProperty = {
        type: 'clientAction',
        payload: ['test'],
      } as unknown as ActionConfig;

      expect(() =>
        result.current.executeClientActionSynchronously(missingActionProperty)
      ).not.toThrow();

      // Verify warning was logged for missing action
      expect(consoleSpy).toHaveBeenCalledWith(
        'executeClientActionSynchronously received config without action property, ignoring action'
      );

      // Test 3: Invalid 'type' value
      const invalidTypeAction = {
        type: 'invalidType',
        action: 'log',
        payload: ['test'],
      } as unknown as ActionConfig;

      expect(() =>
        result.current.executeClientActionSynchronously(invalidTypeAction)
      ).not.toThrow();

      // Test 4: Missing 'payload' property
      // const missingPayloadAction = {
      //   type: 'clientAction',
      //   action: 'log',
      // } as unknown as ActionConfig;

      // expect(() =>
      //   result.current.executeClientActionSynchronously(missingPayloadAction)
      // ).not.toThrow();

      // Test 7: Invalid action type (number instead of string)
      const invalidActionType = {
        type: 'clientAction',
        action: 123,
        payload: [],
      } as unknown as ActionConfig;

      expect(() =>
        result.current.executeClientActionSynchronously(invalidActionType)
      ).not.toThrow();

      // Verify warning was logged for invalid action type
      expect(consoleSpy).toHaveBeenCalledWith(
        'executeClientActionSynchronously received config with invalid action type, expected string'
      );

      // Test 5: Invalid action name (should trigger default case)
      const invalidActionName = {
        type: 'clientAction',
        action: 'nonExistentAction',
        payload: [],
      } as unknown as ActionConfig;

      result.current.executeClientActionSynchronously(invalidActionName);
      // The logger.warn uses template literals and logs to console.log, not console.log directly
      expect(consoleLogSpy).toHaveBeenCalled();

      // Test 6: Completely empty object
      const emptyObject = {} as unknown as ActionConfig;

      expect(() =>
        result.current.executeClientActionSynchronously(emptyObject)
      ).not.toThrow();

      consoleSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });

    it('should handle malformed action config in async execution', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {
        // Mock implementation
      });

      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Test async execution with missing action property
      const missingActionProperty = {
        type: 'clientAction',
        payload: ['test'],
      } as unknown as ActionConfig;

      await act(async () => {
        await expect(
          result.current.executeClientActionAsynchronously(
            missingActionProperty
          )
        ).resolves.toBeUndefined();
      });

      // Verify warning was logged for missing action in async execution
      expect(consoleSpy).toHaveBeenCalledWith(
        'executeClientActionAsynchronously received config without action property, ignoring action'
      );

      consoleSpy.mockRestore();
    });
    it('should handle very large arrays efficiently', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      // Create a large array of actions
      const largeActionArray: ActionConfig[] = Array.from(
        { length: 100 },
        (_, i) => ({
          type: 'clientAction',
          action: 'log',
          payload: [`message ${i}`],
        })
      );
      await act(async () => {
        await result.current.callClientActionsConcurrently(largeActionArray);
      });
      expect(consoleSpy).toHaveBeenCalledTimes(100);
      consoleSpy.mockRestore();
    });
    it('should handle mixed action types in array', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      const mixedActions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['log message'] },
        { type: 'clientAction', action: 'clearErrors', payload: undefined },
        { type: 'clientAction', action: 'closeModal', payload: [] },
      ];
      await act(async () => {
        await result.current.callClientActionsConcurrently(mixedActions);
      });
      expect(consoleSpy).toHaveBeenCalledWith('log message');
      consoleSpy.mockRestore();
    });
    it('should handle concurrent execution with different timing', async () => {
      const executionOrder: string[] = [];
      const consoleSpy = vi
        .spyOn(console, 'log')
        .mockImplementation((message) => {
          executionOrder.push(message);
        });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      const timedActions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['fast'] },
        { type: 'clientAction', action: 'log', payload: ['medium'] },
        { type: 'clientAction', action: 'log', payload: ['slow'] },
      ];
      await act(async () => {
        await result.current.callClientActionsConcurrently(timedActions);
      });
      // All actions should have executed
      expect(executionOrder).toContain('fast');
      expect(executionOrder).toContain('medium');
      expect(executionOrder).toContain('slow');
      consoleSpy.mockRestore();
    });
    it('should handle sequential execution maintaining order', async () => {
      const executionOrder: string[] = [];
      const consoleSpy = vi
        .spyOn(console, 'log')
        .mockImplementation((message) => {
          executionOrder.push(message);
        });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      const sequentialActions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['first'] },
        { type: 'clientAction', action: 'log', payload: ['second'] },
        { type: 'clientAction', action: 'log', payload: ['third'] },
      ];
      await act(async () => {
        await result.current.callClientActionsSequentially(sequentialActions);
      });
      // Actions should execute in order
      expect(executionOrder).toEqual(['first', 'second', 'third']);
      consoleSpy.mockRestore();
    });
    it('should handle concurrency limit correctly', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );
      const limitedActions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['batch1'] },
        { type: 'clientAction', action: 'log', payload: ['batch2'] },
        { type: 'clientAction', action: 'log', payload: ['batch3'] },
        { type: 'clientAction', action: 'log', payload: ['batch4'] },
        { type: 'clientAction', action: 'log', payload: ['batch5'] },
      ];
      await act(async () => {
        await result.current.callClientActionsWithLimit(limitedActions, 2);
      });
      expect(consoleSpy).toHaveBeenCalledTimes(5);
      consoleSpy.mockRestore();
    });
    it('should handle zero concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          debug: true, // Enable debug mode to activate logger
        })
      );
      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['test'] },
      ];
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 0);
      });
      // Should still execute actions even with 0 limit (falls back to sequential)
      // At least the action should execute (we see the 'test' log)
      expect(consoleSpy).toHaveBeenCalledWith('test');
      consoleSpy.mockRestore();
    });

    it('should handle negative concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          debug: true, // Enable debug mode to activate logger
        })
      );
      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['test'] },
      ];
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, -1);
      });
      // Should handle negative limit gracefully (falls back to sequential)
      // At least the action should execute (we see the 'test' log)
      expect(consoleSpy).toHaveBeenCalledWith('test');
      consoleSpy.mockRestore();
    });

    it('should handle empty array with concurrency limit', async () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Empty array should return immediately without any processing
      await act(async () => {
        const promise = result.current.callClientActionsWithLimit([], 2);
        await expect(promise).resolves.toBeUndefined();
      });
    });

    it('should handle very large concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['test1'] },
        { type: 'clientAction', action: 'log', payload: ['test2'] },
      ];

      await act(async () => {
        // Concurrency limit larger than array length should work fine
        await result.current.callClientActionsWithLimit(actions, 100);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });
  });

  describe('withAsync Function', () => {
    it('should handle single action with withAsync', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['withAsync single test'],
      };

      await act(async () => {
        const promise = result.current.withAsync(action);
        expect(promise).toBeInstanceOf(Promise);
        await promise;
      });

      expect(consoleSpy).toHaveBeenCalledWith('withAsync single test');
      consoleSpy.mockRestore();
    });

    it('should handle array with withAsync sequential mode', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['withAsync seq 1'] },
        { type: 'clientAction', action: 'log', payload: ['withAsync seq 2'] },
      ];

      await act(async () => {
        await result.current.withAsync(actions, { mode: 'sequential' });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, 'withAsync seq 1');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'withAsync seq 2');
      consoleSpy.mockRestore();
    });

    it('should handle array with withAsync concurrent mode', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['withAsync conc 1'] },
        { type: 'clientAction', action: 'log', payload: ['withAsync conc 2'] },
      ];

      await act(async () => {
        await result.current.withAsync(actions, { mode: 'concurrent' });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });

    it('should handle array with withAsync concurrent mode and concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['withAsync limit 1'] },
        { type: 'clientAction', action: 'log', payload: ['withAsync limit 2'] },
        { type: 'clientAction', action: 'log', payload: ['withAsync limit 3'] },
      ];

      await act(async () => {
        await result.current.withAsync(actions, {
          mode: 'concurrent',
          concurrencyLimit: 2,
        });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(3);
      consoleSpy.mockRestore();
    });
  });

  describe('evaluateExpression Function', () => {
    it('should handle @param direct substitution', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param'],
        param: 'test value',
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);
      expect(consoleSpy).toHaveBeenCalledWith('test value');
      consoleSpy.mockRestore();
    });

    it('should handle @param template expressions', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param:{name}'],
        param: { name: 'John Doe' },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);
      expect(consoleSpy).toHaveBeenCalledWith('John Doe');
      consoleSpy.mockRestore();
    });

    it('should handle nested objects with dynamic keys and values', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'updateStore',
        payload: [
          {
            '@param:{keyName}': '@param:{value}',
            staticKey: 'staticValue',
          },
        ],
        param: { keyName: 'dynamicKey', value: 'dynamicValue' },
      };

      result.current.executeClientActionSynchronously(action);
      expect(result.current).toBeDefined();
    });

    it('should handle arrays with mixed content types', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param', 'static text', '@param:{nested.value}'],
        param: { nested: { value: 'nested result' } },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      // The payload should be evaluated to the actual values
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle circular reference detection', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Create circular reference
      const circularObj: any = { name: 'test' };
      circularObj.self = circularObj;

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: [circularObj],
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Should not throw error due to circular reference protection
      expect(() =>
        result.current.executeClientActionSynchronously(action)
      ).not.toThrow();

      consoleSpy.mockRestore();
    });

    it('should cache store state to avoid repeated calls', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['$mockState', '$mockState'], // Two references to store
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      // Should execute without errors
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle mixed contexts in arrays with @param and store values', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param:{user.name}', 'male'],
        param: { user: { name: 'Marcus' } },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      // Should log the evaluated array with Marcus and male
      expect(consoleSpy).toHaveBeenCalledWith(['Marcus', 'male']);
      consoleSpy.mockRestore();
    });

    it('should handle complex mixed contexts with @param, static values, and store templates', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param:{user.name}', 'male', '#{mockState}'],
        param: { user: { name: 'Marcus' } },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      // Should execute without errors and process all three types
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle nested objects with mixed expression types', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'updateStore',
        payload: [
          {
            userName: '@param:{user.name}',
            gender: 'male',
            storeValue: '#{mockState}',
            nested: {
              paramValue: '@param:{user.age}',
              staticValue: 'constant',
            },
          },
        ],
        param: { user: { name: 'Marcus', age: 30 } },
      };

      result.current.executeClientActionSynchronously(action);
      expect(result.current).toBeDefined();
    });

    it('should handle store-based templates starting with $ prefix', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param:{user.name}', '$mockState'],
        param: { user: { name: 'Marcus' } },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle JavaScript expressions with js: prefix', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const action: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['@param:{user.name}', 'js:1 + 1'],
        param: { user: { name: 'Marcus' } },
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      result.current.executeClientActionSynchronously(action);

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('createBatch Function', () => {
    it('should create and execute batch sequentially', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const batch = result.current.createBatch();

      // Add actions to batch
      batch.add({ type: 'clientAction', action: 'log', payload: ['batch 1'] });
      batch.add({ type: 'clientAction', action: 'log', payload: ['batch 2'] });

      expect(batch.size()).toBe(2);

      await act(async () => {
        await batch.execute({ mode: 'sequential' });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, 'batch 1');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'batch 2');
      consoleSpy.mockRestore();
    });

    it('should create and execute batch concurrently', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const batch = result.current.createBatch();

      batch.add({
        type: 'clientAction',
        action: 'log',
        payload: ['batch conc 1'],
      });
      batch.add({
        type: 'clientAction',
        action: 'log',
        payload: ['batch conc 2'],
      });

      await act(async () => {
        await batch.execute({ mode: 'concurrent' });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });

    it('should clear batch correctly', () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const batch = result.current.createBatch();

      batch.add({ type: 'clientAction', action: 'log', payload: ['test'] });
      expect(batch.size()).toBe(1);

      batch.clear();
      expect(batch.size()).toBe(0);
    });

    it('should execute empty batch without errors', async () => {
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const batch = result.current.createBatch();

      await act(async () => {
        await expect(batch.execute()).resolves.toBeUndefined();
      });
    });

    it('should execute batch with concurrency limit', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        // Mock implementation
      });
      const { result } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const batch = result.current.createBatch();

      for (let i = 1; i <= 5; i++) {
        batch.add({
          type: 'clientAction',
          action: 'log',
          payload: [`batch limit ${i}`],
        });
      }

      await act(async () => {
        await batch.execute({ mode: 'concurrent', concurrencyLimit: 2 });
      });

      expect(consoleSpy).toHaveBeenCalledTimes(5);
      consoleSpy.mockRestore();
    });
  });
});
